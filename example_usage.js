/**
 * Example usage of LinkedIn Applicants Data Extractor
 * This file demonstrates how to use the extractor with different configurations
 */

const LinkedInApplicantsExtractor = require('./download-linkedin-appliers-data.js');

async function exampleUsage() {
    console.log('🚀 LinkedIn Applicants Data Extractor - Example Usage\n');
    
    // Example LinkedIn applicants URL
    const exampleUrl = 'https://www.linkedin.com/hiring/jobs/4265790910/applicants/25092115684/detail/';
    
    console.log('📋 This example will:');
    console.log('   1. Extract applicant data from LinkedIn');
    console.log('   2. Download resumes (if available)');
    console.log('   3. Export data to CSV and Excel formats');
    console.log('   4. Show extraction statistics\n');
    
    console.log('⚠️  Important Notes:');
    console.log('   - Make sure you are logged into LinkedIn');
    console.log('   - The browser will open in non-headless mode for manual login');
    console.log('   - You may need to solve CAPTCHAs or verify your identity');
    console.log('   - The script includes anti-detection measures and random delays\n');
    
    // Create extractor instance
    const extractor = new LinkedInApplicantsExtractor();
    
    try {
        // Start extraction
        await extractor.extract(exampleUrl);
        
        // Get and display statistics
        const stats = extractor.getStats();
        console.log('\n📊 Final Statistics:');
        console.log(`   📋 Total Applicants: ${stats.totalApplicants}`);
        console.log(`   📄 With Resumes: ${stats.applicantsWithResumes}`);
        console.log(`   💼 With Experience: ${stats.applicantsWithExperience}`);
        console.log(`   🎓 With Education: ${stats.applicantsWithEducation}`);
        
        console.log('\n✅ Example completed successfully!');
        
    } catch (error) {
        console.error('❌ Example failed:', error.message);
        console.log('\n🔧 Troubleshooting Tips:');
        console.log('   - Ensure you are logged into LinkedIn');
        console.log('   - Check if the URL is correct and accessible');
        console.log('   - Verify your internet connection');
        console.log('   - Try running the script again after a few minutes');
    }
}

// Custom configuration example
async function customConfigExample() {
    console.log('\n🔧 Custom Configuration Example\n');
    
    const extractor = new LinkedInApplicantsExtractor();
    
    // You can customize the output directory
    extractor.outputDir = './custom-output';
    extractor.resumesDir = './custom-output/resumes';
    
    // Custom delays for slower extraction (more human-like)
    extractor.delays = {
        short: [2000, 4000],
        medium: [5000, 8000],
        long: [8000, 15000]
    };
    
    console.log('📁 Custom output directory:', extractor.outputDir);
    console.log('⏱️  Custom delays configured for slower, more human-like behavior');
    
    // Note: You would call extractor.extract(url) here with your URL
    console.log('💡 To use: await extractor.extract(yourLinkedInUrl);');
}

// Run examples
if (require.main === module) {
    console.log('🎯 Choose an example to run:');
    console.log('   1. Basic usage example');
    console.log('   2. Custom configuration example');
    
    const choice = process.argv[2] || '1';
    
    switch (choice) {
        case '1':
            exampleUsage().catch(console.error);
            break;
        case '2':
            customConfigExample().catch(console.error);
            break;
        default:
            console.log('❌ Invalid choice. Use: node example_usage.js [1|2]');
            break;
    }
}

module.exports = { exampleUsage, customConfigExample };
