{"name": "linkedin-applicants-extractor-enhanced", "version": "2.0.0", "main": "download-linkedin-appliers-data.js", "description": "Enhanced LinkedIn Applicants Data Extractor with anti-detection measures and robust error handling", "scripts": {"start": "node download-linkedin-appliers-data.js", "example": "node example_usage.js", "check": "node check-prerequisites.js", "install-browsers": "npx playwright install", "test": "node -e \"console.log('Testing dependencies...'); require('./download-linkedin-appliers-data.js'); console.log('✅ All dependencies loaded successfully');\""}, "keywords": ["linkedin", "scraping", "recruitment", "data-extraction", "playwright", "automation", "anti-detection"], "author": "LinkedIn Extractor Enhanced", "license": "MIT", "dependencies": {"fs-extra": "^11.3.0", "playwright": "^1.54.1", "xlsx": "^0.18.5"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "local"}}