/**
 * LinkedIn Applicants Data Extractor - Enhanced Version
 * This script downloads the data from the LinkedIn Appliers page with anti-detection measures.
 * Data to extract:
 * - name
 * - profile description
 * - experience
 * - education
 * - linkedin profile url
 * - download Resumes to a folder with the name of the applier
 *
 * Features:
 * - Anti-detection measures (random delays, realistic user agents)
 * - Robust error handling and recovery
 * - CSV and Excel export
 * - Improved data extraction
 */

const { chromium } = require('playwright');
const fs = require('fs-extra');
const path = require('path');
const XLSX = require('xlsx');

class LinkedInApplicantsExtractor {
    constructor() {
        this.browser = null;
        this.page = null;
        this.applicantsData = [];
        this.outputDir = './linkedin-applicants-data';
        this.resumesDir = path.join(this.outputDir, 'resumes');

        // Anti-detection configurations
        this.userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0'
        ];

        this.delays = {
            short: [1000, 3000],
            medium: [3000, 6000],
            long: [5000, 10000]
        };
    }

    // Utility function for random delays
    async randomDelay(type = 'medium') {
        const [min, max] = this.delays[type];
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        console.log(`⏳ Waiting ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Get random user agent
    getRandomUserAgent() {
        return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
    }

    // Initialize browser with anti-detection measures
    async initBrowser() {
        console.log('🚀 Initializing browser with anti-detection measures...');

        this.browser = await chromium.launch({
            headless: false, // Set to true for production
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        });

        const context = await this.browser.newContext({
            userAgent: this.getRandomUserAgent(),
            viewport: { width: 1920, height: 1080 },
            locale: 'en-US',
            timezoneId: 'America/New_York'
        });

        this.page = await context.newPage();

        // Add extra headers to look more human
        await this.page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        });

        // Override navigator.webdriver
        await this.page.addInitScript(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        });

        console.log('✅ Browser initialized successfully');
    }

    // Navigate to LinkedIn applicants page
    async navigateToApplicantsPage(url) {
        console.log(`🔗 Navigating to: ${url}`);

        try {
            await this.page.goto(url, {
                waitUntil: 'networkidle',
                timeout: 30000
            });

            await this.randomDelay('medium');

            // Check if we need to login
            const isLoginPage = await this.page.locator('input[name="session_key"]').isVisible().catch(() => false);

            if (isLoginPage) {
                console.log('⚠️  LinkedIn login required. Please login manually and press Enter to continue...');
                await new Promise(resolve => {
                    process.stdin.once('data', () => resolve());
                });

                // Wait for navigation after login
                await this.page.waitForLoadState('networkidle');
                await this.randomDelay('long');
            }

            console.log('✅ Successfully navigated to applicants page');

        } catch (error) {
            console.error('❌ Error navigating to page:', error.message);
            throw error;
        }
    }

    // Extract applicant data from the current page
    async extractApplicantData() {
        console.log('📊 Extracting applicant data...');

        try {
            // Wait for the applicant list to load
            await this.page.waitForSelector('[data-test-id="applicant-list"]', { timeout: 10000 });
            await this.randomDelay('short');

            // Get all applicant cards
            const applicantCards = await this.page.locator('[data-test-id="applicant-card"]').all();
            console.log(`📋 Found ${applicantCards.length} applicants`);

            for (let i = 0; i < applicantCards.length; i++) {
                console.log(`\n👤 Processing applicant ${i + 1}/${applicantCards.length}...`);

                try {
                    // Click on the applicant to view details
                    await applicantCards[i].click();
                    await this.randomDelay('medium');

                    // Wait for the detail panel to load
                    await this.page.waitForSelector('[data-test-id="applicant-detail"]', { timeout: 5000 });

                    const applicantData = await this.extractSingleApplicantData();

                    if (applicantData) {
                        this.applicantsData.push(applicantData);
                        console.log(`✅ Extracted data for: ${applicantData.name}`);
                    }

                } catch (error) {
                    console.error(`❌ Error processing applicant ${i + 1}:`, error.message);
                    continue;
                }

                await this.randomDelay('short');
            }

            console.log(`\n🎉 Successfully extracted data for ${this.applicantsData.length} applicants`);

        } catch (error) {
            console.error('❌ Error extracting applicant data:', error.message);
            throw error;
        }
    }

    // Extract data from a single applicant's detail view
    async extractSingleApplicantData() {
        try {
            const applicantData = {
                name: '',
                profileDescription: '',
                experience: [],
                education: [],
                linkedinUrl: '',
                resumeDownloaded: false,
                extractedAt: new Date().toISOString()
            };

            // Extract name
            const nameElement = await this.page.locator('[data-test-id="applicant-name"]').first();
            if (await nameElement.isVisible()) {
                applicantData.name = await nameElement.textContent();
            }

            // Extract profile description/headline
            const descriptionElement = await this.page.locator('[data-test-id="applicant-headline"]').first();
            if (await descriptionElement.isVisible()) {
                applicantData.profileDescription = await descriptionElement.textContent();
            }

            // Extract LinkedIn profile URL
            const profileLinkElement = await this.page.locator('a[href*="/in/"]').first();
            if (await profileLinkElement.isVisible()) {
                applicantData.linkedinUrl = await profileLinkElement.getAttribute('href');
            }

            // Extract experience
            const experienceSection = await this.page.locator('[data-test-id="experience-section"]').first();
            if (await experienceSection.isVisible()) {
                const experienceItems = await experienceSection.locator('.experience-item').all();

                for (const item of experienceItems) {
                    const title = await item.locator('.job-title').textContent().catch(() => '');
                    const company = await item.locator('.company-name').textContent().catch(() => '');
                    const duration = await item.locator('.duration').textContent().catch(() => '');

                    if (title || company) {
                        applicantData.experience.push({
                            title: title.trim(),
                            company: company.trim(),
                            duration: duration.trim()
                        });
                    }
                }
            }

            // Extract education
            const educationSection = await this.page.locator('[data-test-id="education-section"]').first();
            if (await educationSection.isVisible()) {
                const educationItems = await educationSection.locator('.education-item').all();

                for (const item of educationItems) {
                    const degree = await item.locator('.degree').textContent().catch(() => '');
                    const school = await item.locator('.school-name').textContent().catch(() => '');
                    const year = await item.locator('.graduation-year').textContent().catch(() => '');

                    if (degree || school) {
                        applicantData.education.push({
                            degree: degree.trim(),
                            school: school.trim(),
                            year: year.trim()
                        });
                    }
                }
            }

            // Try to download resume
            await this.downloadResume(applicantData.name);

            return applicantData;

        } catch (error) {
            console.error('❌ Error extracting single applicant data:', error.message);
            return null;
        }
    }

    // Download resume for an applicant
    async downloadResume(applicantName) {
        try {
            // Look for resume download button
            const downloadButton = await this.page.locator('[data-test-id="resume-download"]').first();

            if (await downloadButton.isVisible()) {
                console.log(`📄 Downloading resume for ${applicantName}...`);

                // Set up download handling
                const downloadPromise = this.page.waitForDownload();
                await downloadButton.click();
                const download = await downloadPromise;

                // Create safe filename
                const safeFileName = applicantName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
                const fileName = `${safeFileName}_resume.pdf`;
                const filePath = path.join(this.resumesDir, fileName);

                // Ensure directory exists
                await fs.ensureDir(this.resumesDir);

                // Save the file
                await download.saveAs(filePath);
                console.log(`✅ Resume saved: ${fileName}`);

                return true;
            } else {
                console.log(`⚠️  No resume available for ${applicantName}`);
                return false;
            }

        } catch (error) {
            console.error(`❌ Error downloading resume for ${applicantName}:`, error.message);
            return false;
        }
    }

    // Export data to CSV
    async exportToCSV() {
        console.log('📊 Exporting data to CSV...');

        try {
            await fs.ensureDir(this.outputDir);

            // Prepare CSV data
            const csvData = this.applicantsData.map(applicant => ({
                Name: applicant.name,
                'Profile Description': applicant.profileDescription,
                'LinkedIn URL': applicant.linkedinUrl,
                Experience: applicant.experience.map(exp =>
                    `${exp.title} at ${exp.company} (${exp.duration})`
                ).join(' | '),
                Education: applicant.education.map(edu =>
                    `${edu.degree} from ${edu.school} (${edu.year})`
                ).join(' | '),
                'Resume Downloaded': applicant.resumeDownloaded ? 'Yes' : 'No',
                'Extracted At': applicant.extractedAt
            }));

            // Convert to CSV format
            const headers = Object.keys(csvData[0] || {});
            const csvContent = [
                headers.join(','),
                ...csvData.map(row =>
                    headers.map(header =>
                        `"${(row[header] || '').toString().replace(/"/g, '""')}"`
                    ).join(',')
                )
            ].join('\n');

            const csvPath = path.join(this.outputDir, `linkedin_applicants_${Date.now()}.csv`);
            await fs.writeFile(csvPath, csvContent, 'utf8');

            console.log(`✅ CSV exported: ${csvPath}`);
            return csvPath;

        } catch (error) {
            console.error('❌ Error exporting to CSV:', error.message);
            throw error;
        }
    }

    // Export data to Excel
    async exportToExcel() {
        console.log('📊 Exporting data to Excel...');

        try {
            await fs.ensureDir(this.outputDir);

            // Prepare Excel data
            const excelData = this.applicantsData.map(applicant => ({
                'Name': applicant.name,
                'Profile Description': applicant.profileDescription,
                'LinkedIn URL': applicant.linkedinUrl,
                'Experience': applicant.experience.map(exp =>
                    `${exp.title} at ${exp.company} (${exp.duration})`
                ).join('\n'),
                'Education': applicant.education.map(edu =>
                    `${edu.degree} from ${edu.school} (${edu.year})`
                ).join('\n'),
                'Resume Downloaded': applicant.resumeDownloaded ? 'Yes' : 'No',
                'Extracted At': applicant.extractedAt
            }));

            // Create workbook and worksheet
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.json_to_sheet(excelData);

            // Auto-size columns
            const colWidths = Object.keys(excelData[0] || {}).map(key => ({
                wch: Math.max(key.length, 20)
            }));
            worksheet['!cols'] = colWidths;

            XLSX.utils.book_append_sheet(workbook, worksheet, 'Applicants');

            const excelPath = path.join(this.outputDir, `linkedin_applicants_${Date.now()}.xlsx`);
            XLSX.writeFile(workbook, excelPath);

            console.log(`✅ Excel exported: ${excelPath}`);
            return excelPath;

        } catch (error) {
            console.error('❌ Error exporting to Excel:', error.message);
            throw error;
        }
    }

    // Main extraction method
    async extract(url) {
        try {
            console.log('🚀 Starting LinkedIn Applicants Data Extraction...');
            console.log('⚠️  Make sure you are logged into LinkedIn before running this script');

            await this.initBrowser();
            await this.navigateToApplicantsPage(url);
            await this.extractApplicantData();

            if (this.applicantsData.length > 0) {
                const csvPath = await this.exportToCSV();
                const excelPath = await this.exportToExcel();

                console.log('\n🎉 Extraction completed successfully!');
                console.log(`📊 Total applicants processed: ${this.applicantsData.length}`);
                console.log(`📄 CSV file: ${csvPath}`);
                console.log(`📊 Excel file: ${excelPath}`);
                console.log(`📁 Resumes folder: ${this.resumesDir}`);
            } else {
                console.log('⚠️  No applicant data was extracted');
            }

        } catch (error) {
            console.error('❌ Extraction failed:', error.message);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    // Cleanup resources
    async cleanup() {
        console.log('🧹 Cleaning up resources...');

        if (this.browser) {
            await this.browser.close();
            console.log('✅ Browser closed');
        }
    }

    // Get extraction statistics
    getStats() {
        return {
            totalApplicants: this.applicantsData.length,
            applicantsWithResumes: this.applicantsData.filter(a => a.resumeDownloaded).length,
            applicantsWithExperience: this.applicantsData.filter(a => a.experience.length > 0).length,
            applicantsWithEducation: this.applicantsData.filter(a => a.education.length > 0).length
        };
    }
}

// Usage example and main execution
async function main() {
    // Configuration
    const LINKEDIN_APPLICANTS_URL = process.argv[2] || 'https://www.linkedin.com/hiring/jobs/4265790910/applicants/25092115684/detail/';

    if (!LINKEDIN_APPLICANTS_URL.includes('linkedin.com')) {
        console.error('❌ Please provide a valid LinkedIn applicants URL');
        console.log('Usage: node download-linkedin-appliers-data.js <linkedin_applicants_url>');
        process.exit(1);
    }

    const extractor = new LinkedInApplicantsExtractor();

    try {
        await extractor.extract(LINKEDIN_APPLICANTS_URL);

        // Display statistics
        const stats = extractor.getStats();
        console.log('\n📈 Extraction Statistics:');
        console.log(`   Total Applicants: ${stats.totalApplicants}`);
        console.log(`   With Resumes: ${stats.applicantsWithResumes}`);
        console.log(`   With Experience: ${stats.applicantsWithExperience}`);
        console.log(`   With Education: ${stats.applicantsWithEducation}`);

    } catch (error) {
        console.error('❌ Script execution failed:', error.message);
        process.exit(1);
    }
}

// Export the class for use in other modules
module.exports = LinkedInApplicantsExtractor;

// Run the script if called directly
if (require.main === module) {
    main().catch(console.error);
}
