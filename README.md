# LinkedIn Applicants Data Extractor

Este script extrae automáticamente los datos de los aplicantes de tu oferta de trabajo en LinkedIn.

## Características

- ✅ Extrae datos de aplicantes de múltiples páginas
- 📊 Genera archivo Excel con todos los datos
- 📄 Descarga CVs automáticamente
- 🏗️ Organiza archivos en carpetas estructuradas

## Datos extraídos

- **Nombre** del aplicante
- **Descripción** del perfil
- **Experiencia** laboral
- **Educación**
- **Ubicación**
- **URL del perfil de LinkedIn**
- **CV descargado** (Sí/No)
- **Fecha de extracción**

## Instalación

1. Instalar dependencias:
```bash
npm install
```

2. Instalar navegadores de Playwright:
```bash
npx playwright install
```

## Uso

1. Ejecutar el script:
```bash
npm start
```

2. **Pasos importantes:**
   - El navegador se abrirá automáticamente
   - **Inicia sesión en LinkedIn** cuando se abra la página
   - Presiona **ENTER** en la consola cuando hayas iniciado sesión
   - El script comenzará a extraer datos automáticamente

## Estructura de archivos generados

```
linkedin-applicants-data/
├── aplicantes_linkedin_2025-07-15.xlsx  # Datos en Excel
└── resumes/                             # CVs descargados
    ├── john_doe_cv.pdf
    ├── jane_smith_cv.pdf
    └── ...
```

## Configuración

Puedes modificar estos parámetros en el archivo `download-linkedin-appliers-data.js`:

- `baseUrl`: URL base de tu oferta de trabajo
- `maxPages`: Número máximo de páginas a procesar
- `outputDir`: Directorio de salida para los archivos

## Requisitos

- Node.js 16 o superior
- Cuenta de LinkedIn con acceso al panel de reclutamiento
- Conexión a internet estable

## Notas importantes

- **Login manual requerido**: Por seguridad de LinkedIn, el login debe hacerse manualmente
- **Tiempo de ejecución**: Aproximadamente 2-3 minutos por cada 10 aplicantes
- **Rate limiting**: El script incluye delays para evitar ser bloqueado
- **Navegador visible**: Se ejecuta en modo no-headless para permitir login manual

## Solución de problemas

### Error de timeout
Si el script se detiene por timeout:
- Verifica tu conexión a internet
- Asegúrate de estar logueado en LinkedIn
- Reinicia el script

### CVs no se descargan
- Algunos aplicantes pueden no tener CV público
- Verifica permisos de descarga en tu navegador

### Datos incompletos
- LinkedIn puede cambiar su estructura HTML
- Contacta al desarrollador si persisten los errores

## Licencia

MIT License
