/**
 * Configuración personalizable para diferentes trabajos de LinkedIn
 */

const configs = {
    // Tu trabajo actual
    currentJob: {
        jobId: '4265790910',
        applicantId: '25091042294',
        baseUrl: 'https://www.linkedin.com/hiring/jobs/4265790910/applicants/',
        maxPages: 2,
        expectedApplicants: 50
    },
    
    // Plantilla para otros trabajos
    // otherJob: {
    //     jobId: 'JOB_ID_HERE',
    //     applicantId: 'APPLICANT_ID_HERE',
    //     baseUrl: 'https://www.linkedin.com/hiring/jobs/JOB_ID_HERE/applicants/',
    //     maxPages: 1,
    //     expectedApplicants: 25
    // }
};

module.exports = {
    configs,
    
    // Función para obtener configuración activa
    getActiveConfig: () => configs.currentJob,
    
    // Función para cambiar configuración
    setActiveJob: (jobKey) => {
        if (configs[jobKey]) {
            return configs[jobKey];
        }
        throw new Error(`Configuración '${jobKey}' no encontrada`);
    }
};
