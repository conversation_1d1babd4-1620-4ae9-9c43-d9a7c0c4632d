/**
 * Script de verificación de requisitos previos
 * Ejecuta este script antes del extractor principal para verificar que todo esté configurado
 */

const { chromium } = require('playwright');
const fs = require('fs-extra');

async function checkPrerequisites() {
    console.log('🔍 Verificando requisitos previos...\n');
    
    let allChecksPass = true;
    
    // 1. Verificar que Playwright funciona
    console.log('1. 🎭 Verificando Playwright...');
    try {
        const browser = await chromium.launch({ headless: true });
        const page = await browser.newPage();
        await page.goto('https://www.google.com');
        await browser.close();
        console.log('   ✅ Playwright funciona correctamente\n');
    } catch (error) {
        console.log('   ❌ Error con Playwright:', error.message);
        console.log('   💡 Ejecuta: npx playwright install\n');
        allChecksPass = false;
    }
    
    // 2. Verificar permisos de archivos
    console.log('2. 📁 Verificando permisos de archivos...');
    try {
        const testDir = './test-permissions';
        await fs.ensureDir(testDir);
        await fs.writeFile(`${testDir}/test.txt`, 'test');
        await fs.remove(testDir);
        console.log('   ✅ Permisos de archivos OK\n');
    } catch (error) {
        console.log('   ❌ Error de permisos:', error.message);
        allChecksPass = false;
    }
    
    // 3. Verificar conexión a LinkedIn
    console.log('3. 🌐 Verificando conexión a LinkedIn...');
    try {
        const browser = await chromium.launch({ headless: true });
        const page = await browser.newPage();
        
        const response = await page.goto('https://www.linkedin.com', { 
            timeout: 30000,
            waitUntil: 'domcontentloaded'
        });
        
        if (response.status() === 200) {
            console.log('   ✅ Conexión a LinkedIn OK\n');
        } else {
            console.log(`   ⚠️ LinkedIn respondió con código: ${response.status()}\n`);
        }
        
        await browser.close();
    } catch (error) {
        console.log('   ❌ Error conectando a LinkedIn:', error.message);
        console.log('   💡 Verifica tu conexión a internet\n');
        allChecksPass = false;
    }
    
    // 4. Verificar dependencias
    console.log('4. 📦 Verificando dependencias...');
    try {
        require('xlsx');
        require('fs-extra');
        console.log('   ✅ Todas las dependencias instaladas\n');
    } catch (error) {
        console.log('   ❌ Dependencias faltantes:', error.message);
        console.log('   💡 Ejecuta: npm install\n');
        allChecksPass = false;
    }
    
    // Resultado final
    console.log('===============================');
    if (allChecksPass) {
        console.log('🎉 ¡Todos los requisitos están listos!');
        console.log('📋 Puedes ejecutar el extractor con: npm start');
    } else {
        console.log('⚠️ Hay problemas que necesitan resolverse');
        console.log('💡 Soluciona los errores mostrados arriba');
    }
    console.log('===============================\n');
}

// Ejecutar verificación
checkPrerequisites().catch(console.error);
